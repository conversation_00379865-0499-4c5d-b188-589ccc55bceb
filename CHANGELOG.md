# NodeSeek 私信优化脚本 - 更新日志

## [1.1.0] - 2025-07-31

### 🎉 重大更新

#### ✨ 新增功能
- **S3 对象存储支持**：新增 S3 对象存储备份功能
- **双重备份模式**：支持 WebDAV + S3 双重备份
- **多平台兼容**：兼容各种 S3 兼容的对象存储服务
- **智能备份选择**：可选择仅 WebDAV、仅 S3 或双重备份模式
- **S3 连接测试**：新增 连接测试功能，确保配置正确

#### 🔧 功能增强
- **备份配置界面**：全新的备份配置界面，支持多种存储类型配置
- **恢复源选择**：支持选择从 WebDAV 或 S3 恢复数据
- **URL 模式支持**：S3 支持路径风格和虚拟主机风格两种 URL 模式
- **错误处理优化**：改进了错误提示和处理机制

#### 🌐 支持的存储服务

##### WebDAV 服务
- 坚果云
- 其他标准 WebDAV 服务

##### S3 兼容服务
- 缤纷云 S4
- Cloudflare R2对象存储
- 腾讯云对象存储 COS
- 其他兼容 S3 API 的对象存储服务

### 📝 配置变更
- 新增 S3 配置选项
- 备份模式配置（webdav/s3/both）
- 默认恢复源配置

### 🔗 依赖更新
- 新增 AWS SDK 2.1691.0 依赖
- 新增多个 S3 相关域名连接权限

---

## 📖 脚本使用方法

### 🚀 安装步骤

1. **安装用户脚本管理器**
   - Chrome/Edge: [Tampermonkey](https://chromewebstore.google.com/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
   - Firefox: [Tampermonkey](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)
   - Safari: [Tampermonkey](https://apps.apple.com/us/app/tampermonkey/id1482490089)

2. **安装脚本**
   - 点击 [NodeSeek 私信优化脚本](https://greasyfork.org/zh-CN/scripts/544084-nodeseek-%E7%A7%81%E4%BF%A1%E4%BC%98%E5%8C%96%E8%84%9A%E6%9C%AC)
   - Tampermonkey 会自动识别并提示安装

### 🎯 基本使用

1. 访问 [NodeSeek 私信页面](https://www.nodeseek.com/notification#/message?mode=list)
2. 脚本自动运行，私信链接旁会出现"历史私信"按钮
3. 点击"历史私信"查看所有历史聊天记录

### ⚙️ 备份配置

#### 配置入口
- 点击"历史私信" → "备份设置"
- 或使用 Tampermonkey 菜单中的"备份配置"

#### WebDAV 配置
```
服务器地址: https://dav.jianguoyun.com/dav/
用户名: 您的WebDAV用户名
密码: 您的WebDAV密码
备份路径: /nodeseek_messages_backup/
```

#### S3 配置
```
URL 模式: 路径风格 (推荐) 或 虚拟主机风格
S3 端点: https://s3.amazonaws.com (AWS) 或其他服务商端点
Access Key ID: AKIA...
Secret Access Key: 您的密钥
区域: us-east-1 (或其他区域部分服务商可能是 auto)
存储桶: my-backup-bucket
路径前缀: nodeseek-backups/ (可选)
```

#### 常用 S3 端点示例
- **缤纷云 S4**: `https://s3.bitiful.net`
- **Cloudflare R2**: `https://<ACCOUNT_ID>.r2.cloudflarestorage.com`


### 🔄 备份模式

1. **仅 WebDAV**: 只使用 WebDAV 备份
2. **仅 S3**: 只使用 S3 对象存储备份
3. **双重备份**: 同时使用 WebDAV 和 S3 备份（推荐）

### 📥 数据恢复

1. 点击"历史私信" → "恢复备份"
2. 选择恢复源（WebDAV 或 S3）
3. 选择要恢复的备份文件
4. 确认恢复（会覆盖本地数据）

---

## 🌐 CORS 跨域配置说明

### Cloudflare R2 CORS 配置

如果使用 Cloudflare R2 作为 S3 存储，需要配置 CORS 规则：

```json
[
  {
    "AllowedOrigins": [
      "https://www.nodeseek.com"
    ],
    "AllowedMethods": [
      "GET",
      "POST",
      "PUT",
      "DELETE",
      "HEAD"
    ],
    "AllowedHeaders": [
      "*"
    ],
    "ExposeHeaders": [
      "ETag",
      "Content-Length",
      "Content-Type"
    ],
    "MaxAgeSeconds": 3600
  }
]
```

### 其他 S3 服务 CORS 配置


#### 腾讯云 COS CORS 配置
```json
{
  "CORSRules": [
    {
      "AllowedOrigins": ["https://www.nodeseek.com"],
      "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "HEAD"],
      "AllowedHeaders": ["*"],
      "ExposeHeaders": ["ETag", "Content-Length", "Content-Type"],
      "MaxAgeSeconds": 3600
    }
  ]
}
```

### CORS 配置要点

1. **AllowedOrigins**: 必须包含 `https://www.nodeseek.com`
2. **AllowedMethods**: 需要包含 GET、POST、PUT、DELETE、HEAD
3. **AllowedHeaders**: 建议设置为 `*` 或包含常用头部
4. **ExposeHeaders**: 建议包含 ETag、Content-Length、Content-Type
5. **MaxAgeSeconds**: 建议设置为 3600 秒（1小时）

### 配置方法

#### Cloudflare R2
1. 登录 Cloudflare Dashboard
2. 进入 R2 Object Storage
3. 选择对应的存储桶
4. 点击"Settings" → "CORS policy"
5. 添加上述 JSON 配置


#### 其他服务商
请参考各服务商的官方文档进行 CORS 配置。

---

## 🐛 故障排除

### 常见问题

#### S3 连接失败
1. 检查端点 URL 是否正确
2. 验证 Access Key 和 Secret Key
3. 确认存储桶名称和区域设置
4. 检查 CORS 配置是否正确

#### WebDAV 连接失败
1. 验证服务器地址、用户名、密码
2. 检查备份路径是否存在
3. 确认网络连接稳定

#### 备份失败
1. 检查存储空间是否充足
2. 验证权限设置
3. 查看浏览器控制台错误信息

### 调试模式
- 在 Tampermonkey 菜单中开启"调试模式"
- 查看浏览器控制台获取详细日志

---

> 完整代码去Gist查看  [Gist地址](https://gist.githubusercontent.com/likesrt/a00933ce7e6ad1ac1554386596732646/raw/ac6266adcd82311cb94ba358e79e66ef692ff47c/NodeSeek%2520%25E7%25A7%2581%25E4%25BF%25A1%25E4%25BC%2598%25E5%258C%2596%25E8%2584%259A%25E6%259C%25AC)